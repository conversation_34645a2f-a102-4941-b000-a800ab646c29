/**
 * Check what's in the database
 */

const mongoose = require('mongoose');
const Draft = require('./models/Draft');
const Company = require('./models/Company');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/ai-blog-platform', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

async function checkDatabase() {
  console.log('🔍 Checking database contents...\n');

  try {
    // Check companies
    const companies = await Company.find();
    console.log(`🏢 Found ${companies.length} companies:`);
    companies.forEach((company, index) => {
      console.log(`   ${index + 1}. ${company.name} (${company._id})`);
    });

    // Check drafts
    const drafts = await Draft.find().sort({ createdAt: -1 }).limit(5);
    console.log(`\n📝 Found ${drafts.length} recent drafts:`);
    
    if (drafts.length === 0) {
      console.log('   No drafts found in database');
    } else {
      drafts.forEach((draft, index) => {
        console.log(`   ${index + 1}. Draft ${draft._id}`);
        console.log(`      Keyword: ${draft.selectedKeyword || 'Unknown'}`);
        console.log(`      Status: ${draft.status || 'Unknown'}`);
        console.log(`      Created: ${draft.createdAt}`);
        console.log(`      Has content: ${draft.generatedContent ? 'Yes' : 'No'}`);
        if (draft.generatedContent?.contentBlocks) {
          console.log(`      Content blocks: ${draft.generatedContent.contentBlocks.length}`);
        }
        console.log('');
      });
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the check
checkDatabase();
