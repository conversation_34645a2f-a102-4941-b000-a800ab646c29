// Minimal server for image generation
const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');

dotenv.config();

const app = express();
const PORT = 5002;

// Basic middleware
app.use(express.json());
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));

// Serve uploaded images
app.use('/uploads', express.static('uploads'));

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Minimal server is running' });
});

// Image generation endpoint
app.post('/api/images/generate', async (req, res) => {
  try {
    const { prompt, style = 'realistic', imageType = 'featured', draftId, blockId, blogTitle, customTitle } = req.body;

    if (!prompt) {
      return res.status(400).json({ message: 'Prompt is required' });
    }

    console.log(`🎨 Image generation request: "${prompt}" (style: ${style}, type: ${imageType})`);

    // Import image service
    const imageService = require('./services/imageService');
    
    // Use provided blog title or empty string
    const titleForImage = blogTitle || '';
    const result = await imageService.generateImageWithAI(prompt, style, imageType, titleForImage, customTitle);

    // If this is for a specific content block, we could save the association
    if (draftId && blockId) {
      console.log(`📎 Generated image for draft ${draftId}, block ${blockId}`);
      result.draftId = draftId;
      result.blockId = blockId;
    }

    res.json(result);
  } catch (error) {
    console.error('Image generation error:', error);
    res.status(500).json({ 
      success: false,
      message: error.message,
      error: 'Image generation failed'
    });
  }
});

// Fallback for any other API calls
app.use('/api/*', (req, res) => {
  res.status(404).json({ message: 'API endpoint not found in minimal server' });
});

app.listen(PORT, () => {
  console.log(`🚀 Minimal server running on port ${PORT}`);
  console.log(`📋 Health Check: http://localhost:${PORT}/health`);
  console.log(`🎨 Image API: http://localhost:${PORT}/api/images/generate`);
});
