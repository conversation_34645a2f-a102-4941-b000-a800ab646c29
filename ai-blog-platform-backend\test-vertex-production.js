// Production Vertex AI Test Script
require('dotenv').config();
const GeminiService = require('./services/geminiService');
const fs = require('fs');
const path = require('path');

async function testVertexAIProduction() {
  console.log('🚀 Testing Vertex AI Production Setup...\n');
  
  // Comprehensive environment check
  console.log('📋 Environment Verification:');
  const projectId = process.env.GOOGLE_CLOUD_PROJECT || process.env.VERTEX_AI_PROJECT;
  const location = process.env.GOOGLE_CLOUD_LOCATION || process.env.VERTEX_AI_LOCATION || 'us-central1';
  const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS || path.join(__dirname, 'service_account_key.json');
  
  console.log(`   Project ID: ${projectId ? '✅ ' + projectId : '❌ Missing'}`);
  console.log(`   Location: ${location}`);
  console.log(`   Credentials Path: ${credentialsPath}`);
  console.log(`   Credentials File Exists: ${fs.existsSync(credentialsPath) ? '✅ Yes' : '❌ No'}`);
  
  if (fs.existsSync(credentialsPath)) {
    try {
      const credentialsContent = JSON.parse(fs.readFileSync(credentialsPath, 'utf8'));
      console.log(`   Service Account Email: ${credentialsContent.client_email || 'Not found'}`);
      console.log(`   Project ID in Credentials: ${credentialsContent.project_id || 'Not found'}`);
    } catch (error) {
      console.log(`   ❌ Error reading credentials file: ${error.message}`);
    }
  }
  
  console.log('\n🔧 Initializing Vertex AI Service...');
  const geminiService = GeminiService;
  
  // Test 1: Basic Content Generation
  console.log('🧪 Test 1: Basic Content Generation');
  try {
    const basicTest = await geminiService.generateContent(
      'Write a brief professional introduction about solar energy benefits for homeowners.',
      {
        name: 'WattMonk',
        servicesOffered: 'Solar Design, Engineering, Permitting',
        keyword: 'solar energy benefits'
      }
    );
    
    console.log('✅ Basic Content Generation:');
    console.log(`   Source: ${basicTest.source || 'fallback'}`);
    console.log(`   Word Count: ${basicTest.wordCount} words`);
    console.log(`   Keywords: ${basicTest.keywords.slice(0, 3).join(', ')}`);
    console.log(`   Content Preview: ${basicTest.content.substring(0, 150)}...\n`);
    
  } catch (error) {
    console.error('❌ Basic Content Generation Failed:', error.message);
  }
  
  // Test 2: Block Generation with Context
  console.log('🧪 Test 2: Context-Aware Block Generation');
  try {
    const blockTest = await geminiService.generateBlockContent(
      'Write an introduction about photovoltaic software solutions',
      'introduction',
      {
        name: 'WattMonk',
        servicesOffered: 'Solar Design, Engineering, Permitting, Installation Support',
        keyword: 'photovoltaic software'
      },
      {
        keyword: 'photovoltaic software',
        title: 'Photovoltaic Software: A Comprehensive Analysis',
        companyName: 'WattMonk'
      }
    );
    
    console.log('✅ Block Generation:');
    console.log(`   Source: ${blockTest.source || 'fallback'}`);
    console.log(`   Word Count: ${blockTest.wordCount} words`);
    console.log(`   Keywords: ${blockTest.keywords.slice(0, 3).join(', ')}`);
    console.log(`   Content Preview: ${blockTest.content.substring(0, 150)}...\n`);
    
  } catch (error) {
    console.error('❌ Block Generation Failed:', error.message);
  }
  
  // Test 3: Meta Content Generation
  console.log('🧪 Test 3: Meta Content Generation');
  try {
    const metaTest = await geminiService.generateMetaContent(
      'Photovoltaic Software Solutions for Solar Professionals',
      {
        name: 'WattMonk',
        servicesOffered: 'Solar Design, Engineering, Permitting'
      }
    );
    
    console.log('✅ Meta Content Generation:');
    console.log(`   Meta Title: ${metaTest.metaTitle}`);
    console.log(`   Meta Description: ${metaTest.metaDescription}\n`);
    
  } catch (error) {
    console.error('❌ Meta Content Generation Failed:', error.message);
  }
  
  // Test 4: Keyword Suggestions
  console.log('🧪 Test 4: Keyword Suggestions');
  try {
    const keywordTest = await geminiService.generateKeywordSuggestions(
      'photovoltaic software',
      {
        name: 'WattMonk',
        servicesOffered: 'Solar Design, Engineering, Permitting'
      }
    );
    
    console.log('✅ Keyword Suggestions:');
    console.log(`   Generated Keywords: ${keywordTest.slice(0, 5).join(', ')}\n`);
    
  } catch (error) {
    console.error('❌ Keyword Generation Failed:', error.message);
  }
  
  console.log('🏁 Production Test Complete!\n');
  
  // Summary and recommendations
  console.log('📊 Summary:');
  if (projectId && fs.existsSync(credentialsPath)) {
    console.log('✅ Vertex AI credentials are properly configured');
    console.log('✅ Service account key file is present');
    console.log('✅ Content generation should work with real AI');
  } else {
    console.log('⚠️ Vertex AI credentials need attention:');
    if (!projectId) console.log('   - Set GOOGLE_CLOUD_PROJECT in .env');
    if (!fs.existsSync(credentialsPath)) console.log('   - Ensure service_account_key.json is in backend folder');
  }
  
  console.log('\n🔧 Next Steps:');
  console.log('1. Verify all tests show "vertex-ai" as source (not "fallback")');
  console.log('2. Check content quality and word counts');
  console.log('3. Test the full blog generation workflow');
  console.log('4. Monitor Vertex AI usage in Google Cloud Console');
}

// Run the production test
testVertexAIProduction().catch(console.error);
