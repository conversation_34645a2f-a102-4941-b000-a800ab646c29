/**
 * Test link generation with proper API keys loaded
 */

// Load environment variables FIRST
require('dotenv').config();

const linkService = require('./services/linkService');

async function testWithRealAPIs() {
  console.log('🧪 Testing Link Generation with Real APIs...\n');

  const keyword = 'industrial solar maintenance protocols';
  const companyName = 'Wattmonk';

  try {
    console.log('📋 Step 1: Checking API Configuration');
    console.log('====================================');
    console.log(`SERP_API_KEY: ${process.env.SERP_API_KEY ? 'SET' : 'NOT SET'}`);
    console.log(`SERPER_API_KEY: ${process.env.SERPER_API_KEY ? 'SET' : 'NOT SET'}`);
    console.log(`PERPLEXITY_API_KEY: ${process.env.PERPLEXITY_API_KEY ? 'SET' : 'NOT SET'}`);
    console.log(`RAPIDAPI_KEY: ${process.env.RAPIDAPI_KEY ? 'SET' : 'NOT SET'}`);

    console.log('\n📋 Step 2: Generating Links with Real APIs');
    console.log('==========================================');
    
    const links = await linkService.generateInboundOutboundLinks(keyword, companyName);
    
    console.log(`\n✅ Generated ${links.inboundLinks.length} internal links:`);
    links.inboundLinks.forEach((link, index) => {
      console.log(`  ${index + 1}. ${link.text}`);
      console.log(`     URL: ${link.url}`);
      console.log(`     Type: ${link.type}\n`);
    });

    console.log(`✅ Generated ${links.outboundLinks.length} external links:`);
    links.outboundLinks.forEach((link, index) => {
      console.log(`  ${index + 1}. ${link.text}`);
      console.log(`     URL: ${link.url}`);
      console.log(`     Type: ${link.type}`);
      console.log(`     Domain: ${link.domain}\n`);
    });

    // Check if we got real search results vs fallback
    const hasRealSearchResults = links.outboundLinks.some(link => 
      !link.text.includes('Department of Energy Research') &&
      !link.text.includes('NREL Technical Resources') &&
      !link.text.includes('SEIA Industry Data')
    );

    if (hasRealSearchResults) {
      console.log('🎯 SUCCESS: Got real search results from APIs!');
    } else {
      console.log('📋 INFO: Using fallback authority links (APIs may be rate limited)');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testWithRealAPIs();
