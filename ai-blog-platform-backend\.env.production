# Production Environment Configuration
# Copy this file to .env and update with your actual values

# Application
NODE_ENV=production
PORT=5001

# Database
MONGODB_URI=mongodb://localhost:27017/ai-blog-platform-prod

# AI Services
GEMINI_API_KEY=your_gemini_api_key_here
VERTEX_AI_PROJECT_ID=your_vertex_ai_project_id
VERTEX_AI_LOCATION=us-central1

# WordPress Integration
WORDPRESS_BASE_URL=https://your-wordpress-site.com
WORDPRESS_USERNAME=your_wordpress_username
WORDPRESS_APP_PASSWORD=your_wordpress_app_password

# External APIs
PERPLEXITY_API_KEY=your_perplexity_api_key
SERP_API_KEY=your_serp_api_key
SERPER_API_KEY=your_serper_api_key

# AWS S3 (for image storage)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=ap-south-1
AWS_S3_BUCKET=aibucketwattmonk

# Security
JWT_SECRET=your_jwt_secret_here
BCRYPT_ROUNDS=12

# CORS
FRONTEND_URL=http://localhost:3001

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# N8N Integration (if using)
N8N_WEBHOOK_URL=https://your-n8n-instance.com/webhook/wordpress
N8N_API_KEY=your_n8n_api_key
