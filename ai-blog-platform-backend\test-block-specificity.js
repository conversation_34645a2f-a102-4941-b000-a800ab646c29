// Test Block Specificity - Ensure each block generates only appropriate content
require('dotenv').config();
const GeminiService = require('./services/geminiService');

async function testBlockSpecificity() {
  console.log('🧪 Testing Block Content Specificity...\n');
  
  const geminiService = GeminiService;
  
  const testData = {
    keyword: 'photovoltaic software',
    companyContext: {
      name: 'WattMonk',
      servicesOffered: 'Solar Design, Engineering, Permitting',
      keyword: 'photovoltaic software'
    },
    blogContext: {
      keyword: 'photovoltaic software',
      title: 'Photovoltaic Software: A Comprehensive Analysis',
      companyName: 'WattMonk'
    }
  };
  
  console.log('📝 Test 1: Introduction Block Specificity');
  try {
    const intro = await geminiService.generateBlockContent(
      'Write an introduction about photovoltaic software for solar professionals',
      'introduction',
      testData.companyContext,
      testData.blogContext
    );
    
    console.log('✅ Introduction Block Analysis:');
    console.log(`   Word Count: ${intro.wordCount} words (target: 100-150)`);
    console.log(`   Has Headings: ${intro.content.includes('<h') ? '❌ Should not have headings' : '✅ No headings'}`);
    console.log(`   Has Conclusion Phrases: ${/in conclusion|to summarize|in summary|contact us today/i.test(intro.content) ? '❌ Has conclusion content' : '✅ No conclusion content'}`);
    console.log(`   Has Paragraphs: ${intro.content.includes('<p>') ? '✅ Properly formatted' : '❌ Not formatted'}`);
    console.log(`   Company Mentions: ${(intro.content.match(/WattMonk/g) || []).length} (target: 1)`);
    console.log(`   Content Preview: ${intro.content.substring(0, 200)}...\n`);
    
  } catch (error) {
    console.error('❌ Introduction test failed:', error.message);
  }
  
  console.log('📝 Test 2: Section Block Specificity');
  try {
    const section = await geminiService.generateBlockContent(
      'Write about key features and benefits of photovoltaic software',
      'section',
      testData.companyContext,
      testData.blogContext
    );
    
    console.log('✅ Section Block Analysis:');
    console.log(`   Word Count: ${section.wordCount} words (target: 150-250)`);
    console.log(`   Has Headings: ${section.content.includes('<h') ? '❌ Should not have headings' : '✅ No headings'}`);
    console.log(`   Has Introduction Phrases: ${/this article will|in this guide|let's explore/i.test(section.content) ? '❌ Has intro content' : '✅ No intro content'}`);
    console.log(`   Has Conclusion Phrases: ${/in conclusion|to summarize|contact us today/i.test(section.content) ? '❌ Has conclusion content' : '✅ No conclusion content'}`);
    console.log(`   Has Lists/Structure: ${section.content.includes('<ul>') || section.content.includes('<li>') ? '✅ Has structure' : '❌ No structure'}`);
    console.log(`   Company Mentions: ${(section.content.match(/WattMonk/g) || []).length} (target: 1)`);
    console.log(`   Content Preview: ${section.content.substring(0, 200)}...\n`);
    
  } catch (error) {
    console.error('❌ Section test failed:', error.message);
  }
  
  console.log('📝 Test 3: Conclusion Block Specificity');
  try {
    const conclusion = await geminiService.generateBlockContent(
      'Write a conclusion about photovoltaic software with call-to-action',
      'conclusion',
      testData.companyContext,
      testData.blogContext
    );
    
    console.log('✅ Conclusion Block Analysis:');
    console.log(`   Word Count: ${conclusion.wordCount} words (target: 80-120)`);
    console.log(`   Has Headings: ${conclusion.content.includes('<h') ? '❌ Should not have headings' : '✅ No headings'}`);
    console.log(`   Has Introduction Phrases: ${/this article will|let's explore|in this guide/i.test(conclusion.content) ? '❌ Has intro content' : '✅ No intro content'}`);
    console.log(`   Has Summary Phrases: ${/in summary|to summarize|in conclusion/i.test(conclusion.content) ? '✅ Has summary content' : '❌ No summary content'}`);
    console.log(`   Has Call-to-Action: ${/contact|ready to|get started|reach out/i.test(conclusion.content) ? '✅ Has CTA' : '❌ No CTA'}`);
    console.log(`   Company Mentions: ${(conclusion.content.match(/WattMonk/g) || []).length} (target: 1)`);
    console.log(`   Content Preview: ${conclusion.content.substring(0, 200)}...\n`);
    
  } catch (error) {
    console.error('❌ Conclusion test failed:', error.message);
  }
  
  console.log('📝 Test 4: Multiple Block Generation (Context Consistency)');
  try {
    console.log('Generating introduction...');
    const intro2 = await geminiService.generateBlockContent(
      'Write an introduction about solar panel efficiency',
      'introduction',
      { ...testData.companyContext, keyword: 'solar panel efficiency' },
      { ...testData.blogContext, keyword: 'solar panel efficiency', title: 'Solar Panel Efficiency Guide' }
    );
    
    console.log('Generating section...');
    const section2 = await geminiService.generateBlockContent(
      'Write about factors affecting solar panel efficiency',
      'section',
      { ...testData.companyContext, keyword: 'solar panel efficiency' },
      { ...testData.blogContext, keyword: 'solar panel efficiency', title: 'Solar Panel Efficiency Guide' }
    );
    
    console.log('✅ Context Consistency Check:');
    console.log(`   Intro mentions efficiency: ${intro2.content.toLowerCase().includes('efficiency') ? '✅ Yes' : '❌ No'}`);
    console.log(`   Section mentions efficiency: ${section2.content.toLowerCase().includes('efficiency') ? '✅ Yes' : '❌ No'}`);
    console.log(`   Both mention WattMonk: ${intro2.content.includes('WattMonk') && section2.content.includes('WattMonk') ? '✅ Yes' : '❌ No'}`);
    console.log(`   Different content: ${intro2.content !== section2.content ? '✅ Unique content' : '❌ Duplicate content'}`);
    
  } catch (error) {
    console.error('❌ Context consistency test failed:', error.message);
  }
  
  console.log('🏁 Block Specificity Test Complete!\n');
  
  console.log('📊 Summary:');
  console.log('✅ Each block type should generate only appropriate content');
  console.log('✅ Introduction: Sets up the topic (100-150 words)');
  console.log('✅ Section: Provides specific information (150-250 words)');
  console.log('✅ Conclusion: Summarizes and includes CTA (80-120 words)');
  console.log('✅ No block should contain full blog structure');
  console.log('✅ Context should be maintained across blocks');
}

// Run the test
testBlockSpecificity().catch(console.error);
