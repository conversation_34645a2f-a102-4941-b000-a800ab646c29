// Debug content formatting
require('dotenv').config();

async function debugContent() {
  try {
    const geminiService = require('./services/geminiService');
    
    const draftData = {
      selectedKeyword: 'industrial solar maintenance protocols',
      selectedH1: 'Mastering Industrial Solar Maintenance Protocols: A Comprehensive Guide',
      selectedMetaTitle: 'Industrial Solar Maintenance Protocols - Expert Guide 2024',
      selectedMetaDescription: 'Learn essential industrial solar maintenance protocols.',
      companyName: 'WattMonk',
      companyContext: {
        name: 'WattMonk',
        servicesOffered: 'Solar Design, Engineering, Permitting'
      },
      targetWordCount: 800
    };
    
    const result = await geminiService.generateStructuredBlogContent(draftData);
    
    if (result.success && result.content?.content) {
      console.log('🔍 Searching for broken link patterns...\n');
      
      const content = result.content.content;
      
      // Look for broken link patterns
      const brokenPatterns = [
        /https:\/\/[^\s"]+"\s*target="_blank"/g,
        /rel="noopener noreferrer"\s*class="[^"]*">[^<]*<[^>]*>/g,
        /" target="_blank" rel="noopener noreferrer" class="/g
      ];
      
      brokenPatterns.forEach((pattern, index) => {
        const matches = content.match(pattern);
        if (matches) {
          console.log(`❌ Broken Pattern ${index + 1}:`, matches.slice(0, 3));
        }
      });
      
      // Look for proper links
      const properLinks = content.match(/<a href="[^"]*"[^>]*>[^<]*<\/a>/g);
      if (properLinks) {
        console.log('\n✅ Proper Links Found:', properLinks.length);
        console.log('Sample:', properLinks[0]);
      }
      
      // Save sample content for inspection
      const fs = require('fs');
      fs.writeFileSync('debug-content-sample.html', content);
      console.log('\n📄 Full content saved to debug-content-sample.html');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugContent();
