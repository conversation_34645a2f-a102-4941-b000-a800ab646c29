// Test script for Vertex AI setup
require('dotenv').config();
const GeminiService = require('./services/geminiService');

async function testVertexAI() {
  console.log('🧪 Testing Vertex AI Setup...\n');
  
  // Check environment variables
  console.log('📋 Environment Check:');
  console.log(`   GOOGLE_CLOUD_PROJECT: ${process.env.GOOGLE_CLOUD_PROJECT ? '✅ Set (' + process.env.GOOGLE_CLOUD_PROJECT + ')' : '❌ Missing'}`);
  console.log(`   VERTEX_AI_PROJECT: ${process.env.VERTEX_AI_PROJECT ? '✅ Set' : '❌ Missing'}`);
  console.log(`   GOOGLE_CLOUD_LOCATION: ${process.env.GOOGLE_CLOUD_LOCATION || process.env.VERTEX_AI_LOCATION || 'us-central1 (default)'}`);
  console.log(`   GOOGLE_APPLICATION_CREDENTIALS: ${process.env.GOOGLE_APPLICATION_CREDENTIALS ? '✅ Set' : '❌ Missing'}`);
  console.log(`   Service Account Key File: ${require('fs').existsSync('./service_account_key.json') ? '✅ Found' : '❌ Missing'}`);
  console.log(`   GEMINI_API_KEY: ${process.env.GEMINI_API_KEY ? '✅ Set (fallback)' : '❌ Missing'}\n`);
  
  // Initialize service (it's exported as an instance)
  const geminiService = GeminiService;
  
  // Test content generation
  console.log('🤖 Testing Content Generation...');
  try {
    const testPrompt = 'Write a brief introduction about solar energy benefits in 2-3 sentences.';
    const result = await geminiService.generateContent(testPrompt, {
      name: 'WattMonk',
      keyword: 'solar energy benefits'
    });
    
    console.log('✅ Content Generation Test Passed!');
    console.log(`   Generated ${result.wordCount} words`);
    console.log(`   Content preview: ${result.content.substring(0, 100)}...`);
    console.log(`   Keywords found: ${result.keywords.slice(0, 3).join(', ')}\n`);
    
  } catch (error) {
    console.error('❌ Content Generation Test Failed:', error.message);
    console.log('   This might be due to missing Vertex AI credentials or project setup.\n');
  }
  
  // Test meta generation
  console.log('📝 Testing Meta Content Generation...');
  try {
    const metaResult = await geminiService.generateMetaContent(
      'Solar Panel Installation Guide',
      'WattMonk'
    );

    console.log('✅ Meta Generation Test Passed!');
    console.log(`   Meta Title: ${metaResult.metaTitle}`);
    console.log(`   Meta Description: ${metaResult.metaDescription.substring(0, 80)}...\n`);

  } catch (error) {
    console.error('❌ Meta Generation Test Failed:', error.message);
  }

  // Test block generation with context
  console.log('🧩 Testing Context-Aware Block Generation...');
  try {
    const introResult = await geminiService.generateBlockContent(
      'Write an introduction about photovoltaic software',
      'introduction',
      {
        name: 'WattMonk',
        servicesOffered: 'Solar Design, Engineering, Permitting',
        keyword: 'photovoltaic software'
      },
      {
        keyword: 'photovoltaic software',
        title: 'Photovoltaic Software: A Comprehensive Guide',
        companyName: 'WattMonk'
      }
    );

    console.log('✅ Introduction Block Test Passed!');
    console.log(`   Generated ${introResult.wordCount} words`);
    console.log(`   Content preview: ${introResult.content.substring(0, 150)}...\n`);

    // Test section block
    const sectionResult = await geminiService.generateBlockContent(
      'Write about software features and capabilities',
      'section',
      {
        name: 'WattMonk',
        servicesOffered: 'Solar Design, Engineering, Permitting',
        keyword: 'photovoltaic software'
      },
      {
        keyword: 'photovoltaic software',
        title: 'Photovoltaic Software: A Comprehensive Guide',
        companyName: 'WattMonk'
      }
    );

    console.log('✅ Section Block Test Passed!');
    console.log(`   Generated ${sectionResult.wordCount} words`);
    console.log(`   Content preview: ${sectionResult.content.substring(0, 150)}...\n`);

  } catch (error) {
    console.error('❌ Block Generation Test Failed:', error.message);
  }
  
  console.log('🏁 Test Complete!');
  console.log('\n📖 Setup Instructions:');
  console.log('1. Set up Google Cloud Project with Vertex AI API enabled');
  console.log('2. Configure authentication (service account or gcloud auth)');
  console.log('3. Set GOOGLE_CLOUD_PROJECT and GOOGLE_CLOUD_LOCATION in .env');
  console.log('4. For fallback, set GEMINI_API_KEY in .env');
}

// Run the test
testVertexAI().catch(console.error);
