Stack trace:
Frame         Function      Args
0007FFFF8EA0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF7DA0) msys-2.0.dll+0x1FE8E
0007FFFF8EA0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9178) msys-2.0.dll+0x67F9
0007FFFF8EA0  000210046832 (000210286019, 0007FFFF8D58, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8EA0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF8EA0  000210068E24 (0007FFFF8EB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9180  00021006A225 (0007FFFF8EB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEEEAA0000 ntdll.dll
7FFEEE2F0000 KERNEL32.DLL
7FFEEBE00000 KERNELBASE.dll
7FFEEE4B0000 USER32.dll
7FFEEC370000 win32u.dll
7FFEEE7D0000 GDI32.dll
7FFEEC3A0000 gdi32full.dll
7FFEEBD50000 msvcp_win.dll
7FFEEC4E0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFEED5C0000 advapi32.dll
7FFEEDDA0000 msvcrt.dll
7FFEED860000 sechost.dll
7FFEEC9B0000 RPCRT4.dll
7FFEEB1F0000 CRYPTBASE.DLL
7FFEEBBF0000 bcryptPrimitives.dll
7FFEEDEF0000 IMM32.DLL
